/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useState, useRef} from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  Image,
  View,
  FlatList,
  Dimensions,
  NativeSyntheticEvent,
  NativeScrollEvent,
} from 'react-native';
import {SafeAreaView, SafeAreaProvider} from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {IMAGE_CONSTANT, PrimaryColors} from '../../../Utils/Constants';
import strings from '../../../Utils/LocalizedStrings/LocalizedStrings';
import {useNavigation} from '@react-navigation/native';
import IndexStyle from '../../../Theme/IndexStyle';
import Icon from 'react-native-vector-icons/MaterialIcons';

import AsyncStorage from '@react-native-async-storage/async-storage';
import {imgBaseUrl} from '../../../config/apiUrl';
import {ScrollView} from 'react-native-gesture-handler';
import Feather from 'react-native-vector-icons/Feather';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import {getThoughts} from '../../../services/thoughtService';
import {getStudentProfileData} from '../../../services/studentProfileService';
import {getCategoryCounts} from '../../../services/categoryService';
import {getClassList} from '../../../services/classService';

interface CategoryItem {
  id: string;
  name: string;
  image: any;
  totalClasses: number;
  backgroundcolor: string;
}
interface ClassItem {
  id: string;
  firstName?: string;
  lastName?: string;
  className?: string;
  tuitionClasses?: {education?: string; coachingType?: string}[];
  ClassAbout?: {profilePhoto?: string; classesLogo?: string};
  averageRating?: number;
  reviewCount?: number;
}
interface ThoughtItem {
  id: string;
  thoughts: string;
  createdAt: string;
  status: string;
  class: {
    id: string;
    className: string;
    firstName: string;
    lastName: string;
    contactNo: string;
    ClassAbout: {
      classesLogo: string;
    };
  };
}

const Home: React.FC = () => {
  const screenWidth = Dimensions.get('window').width;
  const flatListRef = useRef<FlatList>(null);
  const scrollTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const scrollInterval = 2000;
  const navigation = useNavigation<any>();
  const {isDarkMode} = IndexStyle();
  const [classData, setClassData] = useState<ClassItem[]>([]);
  const [categorydata, setCategorydata] = useState<any>();
  const [categoryList, setCategoryList] = useState<CategoryItem[]>([]);
  const [thought, setThought] = useState<any>([]);
  const [thoughtIndex, setThoughtIndex] = useState(0);
  const [storedUserData, setStoredUserData] = useState<any>(null);
  const thoughtListRef = useRef<FlatList>(null);

  useEffect(() => {
    const updateCategoryList = () => {
      const newCategoryList: CategoryItem[] = [
        {
          id: '1',
          name: 'Education',
          image: IMAGE_CONSTANT.EDUCATION,
          totalClasses: categorydata?.Education || 0,
          backgroundcolor: '#BDC9D7',
        },
        {
          id: '2',
          name: 'Drama',
          image: IMAGE_CONSTANT.DRAMA,
          totalClasses: categorydata?.Drama || 0,
          backgroundcolor: '#EBE2E3',
        },
        {
          id: '3',
          name: 'Music',
          image: IMAGE_CONSTANT.MUSIC,
          totalClasses: categorydata?.Music || 0,
          backgroundcolor: '#E5E1E2',
        },
        {
          id: '4',
          name: 'Art & Craft',
          image: IMAGE_CONSTANT.ARTANDCRAFT,
          totalClasses: categorydata?.['Art & Craft'] || 0,
          backgroundcolor: '#FDF9EE',
        },
        {
          id: '5',
          name: 'Sports',
          image: IMAGE_CONSTANT.SPORTS,
          totalClasses: categorydata?.Sports || 0,
          backgroundcolor: '#D7CBCB',
        },
        {
          id: '6',
          name: 'Languages',
          image: IMAGE_CONSTANT.LANGUAGE,
          totalClasses: categorydata?.Languages || 0,
          backgroundcolor: '##D7CBCB',
        },
        {
          id: '7',
          name: 'Technology',
          image: IMAGE_CONSTANT.TECHNOLOGY,
          totalClasses: categorydata?.Technology || 0,
          backgroundcolor: '#FBF9EA',
        },
        {
          id: '9',
          name: 'Dance',
          image: IMAGE_CONSTANT.DANCE,
          totalClasses: categorydata?.Arts || 0,
          backgroundcolor: '#FDFAEB',
        },
        {
          id: '10',
          name: 'Computer Classes',
          image: IMAGE_CONSTANT.COMPUTER,
          totalClasses: categorydata?.['Computer Classes'] || 0,
          backgroundcolor: '#FBF5DF',
        },
        {
          id: '11',
          name: 'Cooking Class',
          image: IMAGE_CONSTANT.COOKING,
          totalClasses: categorydata?.['Cooking Class'] || 0,
          backgroundcolor: '#FDF6E6',
        },
        {
          id: '12',
          name: 'Garba Classes',
          image: IMAGE_CONSTANT.GARBA,
          totalClasses: categorydata?.['Garba Classes'] || 0,
          backgroundcolor: '#E4DFDB',
        },
        {
          id: '13',
          name: 'Vaidik Maths',
          image: IMAGE_CONSTANT.VAIDIKMATHS,
          totalClasses: categorydata?.['Vaidik Maths'] || 0,
          backgroundcolor: '#C6DBBA',
        },
        {
          id: '14',
          name: 'Gymnastic Classes',
          image: IMAGE_CONSTANT.GYMNASTIC,
          totalClasses: categorydata?.['Gymnastic Classes'] || 0,
          backgroundcolor: '#FBF9EA',
        },
        {
          id: '15',
          name: 'Yoga Classes',
          image: IMAGE_CONSTANT.YOGA,
          totalClasses: categorydata?.['Yoga Classes'] || 0,
          backgroundcolor: '#E4DFDB',
        },
        {
          id: '16',
          name: 'Aviation Classes',
          image: IMAGE_CONSTANT.AVIATION,
          totalClasses: categorydata?.['Aviation Classes'] || 0,
          backgroundcolor: '#C6DBBA',
        },
        {
          id: '17',
          name: 'Designing Classes',
          image: IMAGE_CONSTANT.DESIGNING,
          totalClasses: categorydata?.['Designing Classes'] || 0,
          backgroundcolor: '#C6DBBA',
        },
      ];
      setCategoryList(newCategoryList);
    };

    if (categorydata) {
      updateCategoryList();
    }
  }, [categorydata]);

  const startAutoScroll = () => {
    if (scrollTimerRef.current) {
      clearInterval(scrollTimerRef.current);
    }
    scrollTimerRef.current = setInterval(() => {
      let nextIndex = currentIndex + 1;
      if (nextIndex >= categoryList.length) {
        nextIndex = 0;
      }

      flatListRef.current?.scrollToIndex({
        index: nextIndex,
        animated: true,
      });
      setCurrentIndex(nextIndex);
    }, scrollInterval);
  };

  useEffect(() => {
    if (categoryList.length === 0) {
      return;
    }
    startAutoScroll();
    return () => {
      if (scrollTimerRef.current) {
        clearInterval(scrollTimerRef.current);
      }
    };
  });
  useEffect(() => {
    const interval = setInterval(() => {
      if (!thought.thoughts || thought.thoughts.length === 0) {
        return;
      }

      const nextIndex = (thoughtIndex + 1) % thought.thoughts.length;

      thoughtListRef.current?.scrollToIndex({
        index: nextIndex,
        animated: true,
      });

      setThoughtIndex(nextIndex);
    }, 3000);

    return () => clearInterval(interval);
  }, [thoughtIndex, thought.thoughts]);

  const handleThoughtScroll = (
    event: NativeSyntheticEvent<NativeScrollEvent>,
  ) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    const newIndex = Math.round(offsetX / (screenWidth * 0.8));
    setThoughtIndex(newIndex);
  };
  const handleScrollBegin = () => {
    if (scrollTimerRef.current) {
      clearInterval(scrollTimerRef.current);
    }
  };

  const fetchStoredUserData = async () => {
    try {
      const userDataString = await AsyncStorage.getItem('userData');
      console.log('Raw AsyncStorage userData:', userDataString);
      if (userDataString) {
        const userData = JSON.parse(userDataString);
        console.log('Parsed userData:', userData);
        setStoredUserData(userData.data);
      } else {
        console.log('No userData found in AsyncStorage');
      }
    } catch (error) {
      console.error('Error retrieving userData from AsyncStorage:', error);
    }
  };

  useEffect(() => {
    fetchStoredUserData();
    fetchStudentProfileData();
    fetchClassList();
    getCategorycount();
    fetchThoughts();
  }, []);

  const fetchThoughts = async () => {
    try {
      const data = await getThoughts();
      setThought(data);
    } catch (error) {
      console.error('Error fetching thoughts:', error);
    }
  };

  const renderThoughtItem = ({item}: {item: ThoughtItem}) => {
    const classLogo = item.class.ClassAbout.classesLogo;
    const imageUrl = classLogo ? `${imgBaseUrl}/${classLogo}` : null;
    const teacherName = `${item.class.firstName} ${item.class.lastName}`;
    const className = item.class.className;
    return (
      <View style={[BaseStyle.modernThoughtCard, {
        width: screenWidth * 0.85,
        backgroundColor: isDarkMode ? '#1A1A1A' : PrimaryColors.WHITE,
        shadowColor: isDarkMode ? '#000000' : '#000000',
      }]}>
        <View style={BaseStyle.modernThoughtQuote}>
          <FontAwesome
            name="quote-left"
            size={24}
            color={'#FD904B'}
          />
        </View>

        <View style={BaseStyle.modernThoughtHeader}>
          <View style={BaseStyle.modernThoughtImageContainer}>
            {imageUrl ? (
              <Image
                source={{uri: imageUrl}}
                style={BaseStyle.modernThoughtImage}
                resizeMode="cover"
              />
            ) : (
              <View style={BaseStyle.modernThoughtImagePlaceholder}>
                <Icon name="person" size={24} color="#FD904B" />
              </View>
            )}
          </View>
          <View style={BaseStyle.modernThoughtInfo}>
            <Text style={[BaseStyle.modernThoughtTeacherName, {
              color: isDarkMode ? '#FFFFFF' : '#1F1F39',
            }]}>
              {teacherName}
            </Text>
            <Text style={[BaseStyle.modernThoughtClassName, {
              color: isDarkMode ? '#B0B0B0' : '#666',
            }]}>
              {className}
            </Text>
          </View>
        </View>

        <Text style={[BaseStyle.modernThoughtText, {
          color: isDarkMode ? '#D9D9D9' : '#555',
        }]}>
          {item.thoughts}
        </Text>
      </View>
    );
  };

  const fetchStudentProfileData = async () => {
    try {
      const data = await getStudentProfileData();
      const profile = data.data.profile || {};
      console.log('PROFILE DATA OBJECT HOME SCREEN:::', profile);
      console.log('PARSED DATA:', data);
    } catch (err) {
      console.log('ERROR IN GET STUDENT PROFILES DATA:', err);
    }
  };

  const OnProfile = (classId: string) => {
    console.log('ON CLICK PROFILE');
    navigation.navigate('TutorProfile', {classId: classId});
  };

  const fetchClassList = async (pageNumber: number = 1) => {
    try {
      const data = await getClassList(pageNumber, 5);
      setClassData(data.data);
    } catch (err) {
      console.log('ERROR IN GET CLASS LIST::', err);
    }
  };

  const getCategorycount = async () => {
    try {
      const data = await getCategoryCounts();
      setCategorydata(data);
    } catch (error) {
      console.error('Error fetching category counts:', error);
    }
  };

  const renderItem = ({item}: {item: ClassItem}) => {
    const profilePhoto = item.ClassAbout?.classesLogo;
    const imageUrl = profilePhoto ? `${imgBaseUrl}/${profilePhoto}` : null;
    const rating = item.averageRating ?? 'N/A';
    const reviewCount = item.reviewCount ?? 'N/A';

    return (
      <TouchableOpacity
        style={[
          BaseStyle.modernTutorCard,
          {
            backgroundColor: isDarkMode ? '#1A1A1A' : PrimaryColors.WHITE,
            shadowColor: isDarkMode ? '#000000' : '#000000',
          },
        ]}
        onPress={() => {
          OnProfile(item.id);
        }}>
        <View style={BaseStyle.modernTutorImageWrapper}>
          <View style={BaseStyle.modernTutorImageContainer}>
            {imageUrl ? (
              <Image
                source={{uri: imageUrl}}
                style={BaseStyle.modernTutorImage}
                resizeMode="cover"
              />
            ) : (
              <View style={BaseStyle.modernTutorPlaceholder}>
                <Icon name="person" size={32} color="#FD904B" />
              </View>
            )}
          </View>
        </View>
        <View style={BaseStyle.modernTutorInfo}>
          <Text style={[BaseStyle.modernTutorName, {
            color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
          }]}>
            {(item.firstName ?? '') + ' ' + (item.lastName ?? '')}
          </Text>
          <View style={BaseStyle.modernTutorRating}>
            <Image
              resizeMode="contain"
              source={IMAGE_CONSTANT.STAR}
              style={BaseStyle.modernStarIcon}
            />
            <Text style={BaseStyle.modernRatingText}>
              {`${rating}.0 (${reviewCount} reviews)`}
            </Text>
          </View>
        </View>
        <View style={BaseStyle.modernTutorAction}>
          <TouchableOpacity
            style={BaseStyle.modernArrowButton}
            onPress={() => OnProfile(item.id)}>
            <Feather name="arrow-right" size={20} color={'#FD904B'} />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  const renderCategoryItem = ({item}: {item: CategoryItem}) => (
    <TouchableOpacity
      style={[
        BaseStyle.modernCategoryCard,
        {
          backgroundColor: isDarkMode ? '#1A1A1A' : PrimaryColors.WHITE,
          shadowColor: isDarkMode ? '#000000' : '#000000',
        },
      ]}
      onPress={() => {
        console.log('ON CLICK CATEGFORY::::', item.name);
        // navigation.navigate('ClassList',{category:item.name});
        navigation.navigate(strings.Home.SEARCH, {category: item.name});
      }}>
      <View style={BaseStyle.modernCategoryImageContainer}>
        <Image
          source={item.image}
          style={BaseStyle.modernCategoryImage}
          resizeMode="contain"
        />
      </View>
      <View style={BaseStyle.modernCategoryContent}>
        <Text style={[BaseStyle.modernCategoryTitle, {
          color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
        }]}>{item.name}</Text>
        <Text style={BaseStyle.modernCategoryCount}>
          {item.totalClasses} classes
        </Text>
      </View>
    </TouchableOpacity>
  );

  const quickLinks = [
    {title: 'Uwhiz', icon: IMAGE_CONSTANT.UWHIZLOGO},
    {title: 'Daily Quiz', icon: IMAGE_CONSTANT.DAILYQUIZ},
    {title: 'Find Classes', icon: IMAGE_CONSTANT.SEARCHCLASSES},
    {title: 'Profile', icon: IMAGE_CONSTANT.PROFILE},
  ];

  const firstName = storedUserData?.user?.firstName ?? 'Guest';
  const lastName = storedUserData?.user?.lastName ?? '';
  return (
    <SafeAreaProvider>
      <View style={BaseStyle.modernHeader}>
        <View style={BaseStyle.headerContent}>
          <View style={BaseStyle.userSection}>
            <View style={BaseStyle.welcomeTextContainer}>
              <Text style={BaseStyle.welcomeText}>Welcome back,</Text>
              <Text
                numberOfLines={1}
                style={BaseStyle.modernUsername}>{`${firstName} ${lastName}`}</Text>
              <Text style={BaseStyle.modernSubtitle}>Let's start learning today</Text>
            </View>
          </View>
          <View style={BaseStyle.headerActions}>
            <TouchableOpacity
              style={BaseStyle.modernActionButton}
              onPress={() => {
                navigation.navigate('StudentProfile');
              }}>
              <Ionicons
                name="person-circle-outline"
                size={28}
                color={'#FFFFFF'}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={[BaseStyle.modernActionButton, BaseStyle.coinButton]}
              onPress={() => {
                navigation.navigate('Payment');
              }}>
              <Image
                resizeMode="contain"
                source={IMAGE_CONSTANT.NEWUESTCOIN}
                style={BaseStyle.modernCoinIcon}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={BaseStyle.modernActionButton}
              onPress={() => {
                navigation.navigate('Wishlist');
              }}>
              <Feather
                name="heart"
                size={26}
                color="#FFFFFF"
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>
      <ScrollView
        nestedScrollEnabled={true}
        style={[BaseStyle.modernScrollView, {
          backgroundColor: isDarkMode ? PrimaryColors.BLACK : PrimaryColors.WHITE,
        }]}>
        <SafeAreaView
          style={[BaseStyle.modernSafeArea, {
            backgroundColor: isDarkMode ? PrimaryColors.BLACK : PrimaryColors.WHITE,
          }]}
          edges={['left', 'right']}>
          <View style={[BaseStyle.modernMainContainer, {
            backgroundColor: isDarkMode ? PrimaryColors.BLACK : PrimaryColors.WHITE,
          }]}>
                          <View style={BaseStyle.modernSection}>
              <Text style={[BaseStyle.modernSectionTitle, {
                color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
              }]}>
                Quick Links
              </Text>
              <View style={BaseStyle.modernQuickLinksGrid}>
                {quickLinks.map((item, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      BaseStyle.modernQuickLinkCard,
                      {
                        backgroundColor: isDarkMode ? '#1A1A1A' : PrimaryColors.WHITE,
                        shadowColor: isDarkMode ? '#000000' : '#000000',
                      },
                    ]}
                    onPress={() => console.log(item.title)}>
                    <View style={BaseStyle.modernQuickLinkIconContainer}>
                      <Image
                        resizeMode="contain"
                        source={item.icon}
                        style={BaseStyle.modernQuickLinkIcon}
                      />
                    </View>
                    <Text style={[BaseStyle.modernQuickLinkText, {
                      color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
                    }]}>
                      {item.title}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            <View style={BaseStyle.modernSection}>
              <Text style={[BaseStyle.modernSectionTitle, {
                color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
              }]}>
                Choose Your Category
              </Text>
              <FlatList
                ref={flatListRef}
                data={categoryList}
                horizontal
                showsHorizontalScrollIndicator={false}
                keyExtractor={item => item.id}
                contentContainerStyle={{paddingHorizontal: 20, paddingRight: 40}}
                onScrollBeginDrag={handleScrollBegin}
                decelerationRate="fast"
                snapToInterval={180}
                renderItem={renderCategoryItem}
              />
            </View>
            <View style={BaseStyle.modernSection}>
              <Text style={[BaseStyle.modernSectionTitle, {
                color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
              }]}>
                Meet Your Top Tutors
              </Text>
              <FlatList
                data={classData}
                keyExtractor={item => item.id}
                renderItem={renderItem}
                contentContainerStyle={{paddingHorizontal: 20}}
                showsVerticalScrollIndicator={false}
              />
            </View>
            <View style={[BaseStyle.modernSection, {paddingBottom: 120}]}>
              <Text style={[BaseStyle.modernSectionTitle, {
                color: isDarkMode ? PrimaryColors.WHITE : PrimaryColors.BLACK,
              }]}>
                What Our Community Thinks
              </Text>
              <FlatList
                ref={thoughtListRef}
                data={thought.thoughts || []}
                renderItem={renderThoughtItem}
                keyExtractor={(_, index) => index.toString()}
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                snapToAlignment="center"
                decelerationRate="fast"
                snapToInterval={screenWidth * 0.85}
                contentContainerStyle={{
                  paddingHorizontal: 20,
                }}
                onScroll={handleThoughtScroll}
                scrollEventThrottle={16}
              />
            </View>
          </View>
        </SafeAreaView>
      </ScrollView>
    </SafeAreaProvider>
  );
};

export default Home;

const BaseStyle = StyleSheet.create({
  content: {
    flex: 1,
    paddingHorizontal: 10,
  },
  homeGrid: {
    flexDirection: 'row',
    justifyContent: 'space-evenly',
  },
  gridWrapper: {
    width: '30%',
    height: '80%',
    marginBottom: 20,
    borderRadius: 16,
    backgroundColor: PrimaryColors.WHITE,
  },
  // Modern Header Styles
  modernHeader: {
    backgroundColor: PrimaryColors.BLACK,
    paddingTop: '12%',
    paddingBottom: '6%',
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  userSection: {
    flex: 1,
  },
  welcomeTextContainer: {
    flex: 1,
  },
  welcomeText: {
    color: '#B0B0B0',
    fontSize: 14,
    fontWeight: '400',
    marginBottom: 2,
  },
  modernUsername: {
    color: PrimaryColors.WHITE,
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 4,
  },
  modernSubtitle: {
    color: '#FD904B',
    fontSize: 16,
    fontWeight: '500',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  modernActionButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  coinButton: {
    backgroundColor: 'rgba(253, 144, 75, 0.15)',
    borderColor: 'rgba(253, 144, 75, 0.3)',
  },
  modernCoinIcon: {
    height: 24,
    width: 24,
    tintColor: '#FD904B',
  },
  // Legacy Header Styles (keeping for compatibility)
  headercomponent: {
    height: '18%',
    width: '100%',
    flexDirection: 'row',
    paddingHorizontal: 10,
    backgroundColor: PrimaryColors.BLACK,
    borderColor: PrimaryColors.WHITE,
    justifyContent: 'space-evenly',
  },
  headername: {
    borderColor: 'white',
    flexDirection: 'row',
    width: '70%',
    alignItems: 'center',
  },
  coinicon: {
    width: '10%',
    marginTop: '16%',
  },
  profileicon: {
    width: '10%',
    justifyContent: 'center',
    paddingRight: '2%',
    alignItems: 'flex-end',
  },
  username: {
    color: PrimaryColors.WHITE,
    fontSize: 30,
    fontWeight: 'bold',
  },
  nameEmailWrapper: {
    flexDirection: 'column',
  },
  email: {
    color: PrimaryColors.WHITE,
    fontSize: 14,
    marginTop: 2,
  },
  smallcoin: {
    height: 40,
    width: 40,
    tintColor: PrimaryColors.WHITE,
  },
  initialsCircle: {
    backgroundColor: PrimaryColors.BLACK,
    width: 30,
    height: 30,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  initialsText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 15,
  },
  cointext: {
    width: '10%',
    paddingBottom: '4%',
    justifyContent: 'flex-end',
    paddingRight: '2%',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
    marginLeft: 10,
  },
  categoryCard: {
    marginLeft: 10,
    borderRadius: 25,

    marginTop: '2%',
  },
  categoryImage: {
    height: 120,
    width: 200,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 15,
    paddingHorizontal: 8,
    marginLeft: 16,
    marginTop: 12,
    width: '80%',
    backgroundColor: '#f2f2f2',
  },
  itemContainer: {
    flexDirection: 'row',
    borderRadius: 20,
    padding: 12,
    marginBottom: 12,
    alignItems: 'center',
  },
  imageWrapper: {
    width: '25%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageContainer: {
    height: 80,
    width: 80,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    backgroundColor: '#e6e6e6',
  },
  image: {
    height: '100%',
    width: '100%',
  },
  infoWrapper: {
    width: '55%',
    paddingLeft: 8,
    justifyContent: 'center',
  },
  nameText: {
    fontSize: 14,
    fontWeight: '700',
    marginBottom: 2,
  },
  subText: {
    fontSize: 15,
    fontWeight: '500',
  },
  buttonWrapper: {
    width: '20%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    marginTop: '7%',
    paddingVertical: 2,
    alignItems: 'center',
    borderRadius: 6,
    backgroundColor: '#FFEBF0',
  },
  buttonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FF914D',
  },
  thoughtContainer: {
    width: '100%',
    flexDirection: 'row',
    backgroundColor: PrimaryColors.WHITE,
    borderRadius: 20,
    padding: 12,
    marginBottom: 12,
    alignItems: 'center',
    marginHorizontal: 10,
  },
  imageWrapperthought: {
    width: '25%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageContainerthought: {
    height: 60,
    width: 60,
    borderRadius: 30,
    borderColor: PrimaryColors.CARDBORDER,
    borderWidth: 0.5,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    backgroundColor: '#e6e6e6',
  },
  imagethought: {
    height: '100%',
    width: '100%',
  },
  contentWrapper: {
    width: '75%',
    paddingLeft: 10,
    justifyContent: 'center',
  },
  thoughtText: {
    fontSize: 16,
    fontWeight: '500',
    color: PrimaryColors.BLACK,
    marginBottom: 4,
  },
  className: {
    fontSize: 14,
    fontWeight: '600',
    color: PrimaryColors.BLACK,
  },
  teacherName: {
    fontSize: 12,
    fontWeight: '400',
    color: PrimaryColors.GRAYSHADOW,
    marginTop: 2,
  },
  shadowBox: {
    backgroundColor: PrimaryColors.WHITE,
    padding: 16,
    borderRadius: 20,
    borderWidth: 1,
  },
  blackshadowBox: {
    backgroundColor: PrimaryColors.BLACK,
    padding: 16,
    borderRadius: 8,
    shadowColor: '#FFF',
    shadowOffset: {width: 3, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 2, // for Android shadow
  },
  grayshadowBox: {
    backgroundColor: '#1D1D1D',
    padding: 16,
    borderRadius: 8,
    shadowColor: '#FFF',
    shadowOffset: {width: 3, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 2, // for Android shadow
  },
  textShadow: {
    marginBottom: '2%',
    color: '#1F1F39',
    textShadowColor: 'rgba(0, 0, 0, 0.30)',
    textShadowOffset: {width: 1, height: 2},
    textShadowRadius: 5,
  },
  examcard: {
    width: '90%',
    height: '100%',
    padding: '1%',
    borderRadius: 14,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sponserdview: {
    width: '100%',
    height: '15%',
    paddingLeft: '3%',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  examname: {
    width: '100%',
    height: '20%',
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingLeft: '3%',
    paddingTop: '1%',
  },
  twoButton: {
    width: '100%',
    height: '20%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: '2%',
  },
  viewandapply: {
    flexDirection: 'row',

    padding: '2%',
    borderRadius: 10,
    width: '80%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  footerspon: {
    width: '100%',
    height: '15%',
    justifyContent: 'center',
    paddingLeft: '3%',
  },
  card: {
    width: 160,
    height: 100,
    borderRadius: 16,
    margin: 10,
    justifyContent: 'flex-end',
    overflow: 'visible',
    alignItems: 'center',
    paddingBottom: 12,
  },
  imagecard: {
    width: '150%',
    height: '150%',
    borderRadius: 16,
  },
  labelContainer: {
    position: 'absolute',
    bottom: 10,
    right: 0,
    backgroundColor: 'white',
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    paddingVertical: 4,
    paddingHorizontal: 10,
  },
  labelText: {
    color: '#555',
    fontWeight: '600',
    fontSize: 14,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalBox: {
    width: '80%',
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 5,
    backgroundColor: PrimaryColors.ORANGEFORTOGGLE,
    paddingVertical: 10,
    borderRadius: 5,
    alignItems: 'center',
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  icon: {
    height: 40,
    width: 40,
    marginRight: 10,
  },
  quickCard: {
    width: '47%',
    marginBottom: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 15,
  },
  // Modern Section Styles
  modernSection: {
    marginTop: 24,
    paddingHorizontal: 20,
  },
  modernSectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 20,
    letterSpacing: -0.5,
  },
  modernQuickLinksGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
  },
  modernQuickLinkCard: {
    width: '47%',
    aspectRatio: 1.2,
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  modernQuickLinkIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 16,
    backgroundColor: 'rgba(253, 144, 75, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  modernQuickLinkIcon: {
    height: 32,
    width: 32,
    tintColor: '#FD904B',
  },
  modernQuickLinkText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    letterSpacing: -0.2,
  },
  // Modern Category Card Styles
  modernCategoryCard: {
    width: 160,
    height: 200,
    borderRadius: 20,
    marginRight: 16,
    padding: 16,
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 10,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  modernCategoryImageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(253, 144, 75, 0.08)',
    borderRadius: 16,
    marginBottom: 12,
  },
  modernCategoryImage: {
    width: 64,
    height: 64,
    tintColor: '#FD904B',
  },
  modernCategoryContent: {
    alignItems: 'center',
  },
  modernCategoryTitle: {
    fontSize: 16,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 4,
    letterSpacing: -0.3,
  },
  modernCategoryCount: {
    fontSize: 12,
    fontWeight: '500',
    color: '#888888',
    textAlign: 'center',
  },
  // Modern Tutor Card Styles
  modernTutorCard: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 20,
    padding: 16,
    marginBottom: 16,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  modernTutorImageWrapper: {
    marginRight: 16,
  },
  modernTutorImageContainer: {
    width: 64,
    height: 64,
    borderRadius: 20,
    overflow: 'hidden',
    backgroundColor: '#F5F5F5',
  },
  modernTutorImage: {
    width: '100%',
    height: '100%',
  },
  modernTutorPlaceholder: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(253, 144, 75, 0.1)',
  },
  modernTutorInfo: {
    flex: 1,
  },
  modernTutorName: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 6,
    letterSpacing: -0.3,
  },
  modernTutorRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modernStarIcon: {
    height: 14,
    width: 14,
    marginRight: 6,
  },
  modernRatingText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#888888',
  },
  modernTutorAction: {
    marginLeft: 12,
  },
  modernArrowButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(253, 144, 75, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Modern Thought Card Styles
  modernThoughtCard: {
    borderRadius: 24,
    padding: 24,
    marginHorizontal: 8,
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.12,
    shadowRadius: 20,
    elevation: 12,
    borderWidth: 0.5,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  modernThoughtQuote: {
    position: 'absolute',
    top: 20,
    right: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(253, 144, 75, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modernThoughtHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  modernThoughtImageContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    overflow: 'hidden',
    backgroundColor: '#F5F5F5',
    marginRight: 16,
  },
  modernThoughtImage: {
    width: '100%',
    height: '100%',
  },
  modernThoughtImagePlaceholder: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(253, 144, 75, 0.1)',
  },
  modernThoughtInfo: {
    flex: 1,
  },
  modernThoughtTeacherName: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
    letterSpacing: -0.3,
  },
  modernThoughtClassName: {
    fontSize: 14,
    fontWeight: '500',
  },
  modernThoughtText: {
    fontSize: 16,
    lineHeight: 26,
    fontWeight: '400',
    letterSpacing: -0.2,
  },
});
